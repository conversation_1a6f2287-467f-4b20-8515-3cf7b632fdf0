:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11400e01
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11400e03
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11400f01
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11400f39
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11400f3a
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11400f3b
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11400f3d
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11400f41
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11400f43
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11400f44
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11400f45
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11400f46
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11410105
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11410107
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x1141010b
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x1141010c
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not available for reading : 0x11500606
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11510306
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600104
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600106
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600204
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600207
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600208
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600209
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600210
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600301
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600304
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600305
2025-05-24 12:29:06.127   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600307
2025-05-24 12:29:06.128   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600309
2025-05-24 12:29:06.128   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x1160030c
2025-05-24 12:29:06.128   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x11600703
2025-05-24 12:29:06.128   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x1540010a
2025-05-24 12:29:06.128   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not available for reading : 0x15400b80
2025-05-24 12:29:06.128   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not available for reading : 0x15400b81
2025-05-24 12:29:06.128   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x15400bb0
2025-05-24 12:29:06.128   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x15400f03
2025-05-24 12:29:06.128   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x15410511
2025-05-24 12:29:06.128   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x17600309
2025-05-24 12:29:06.128   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x1760030a
2025-05-24 12:29:06.128   769-769   CAR.Proper...ServiceIds com.android.car                      E  propId is not writable : 0x23400103
2025-05-24 12:29:06.273   769-769   CarUserService          com.android.car                      E  onUxRestrictionsChanged(): no mICarServiceHelper
2025-05-24 12:29:06.313   769-839   CarPowerMa...entService com.android.car                      E  Failed to notify car power policy daemon: the daemon is not ready
2025-05-24 12:29:06.325   769-839   CarPowerMa...entService com.android.car                      E  Failed to notify car power policy daemon: the daemon is not ready
2025-05-24 12:29:06.369   769-822   CarDrivingStateService  com.android.car                      E  Received property event for unhandled propId=289408009
2025-05-24 12:29:06.446   769-837   CAR.Vendor...Controller com.android.car                      E  Failed to start or bind service VendorService{component=com.android.car.cartelemetryapp/.CarMetricsCollectorService, bind=BIND, trigger=ASAP, userScope=SYSTEM}
2025-05-24 12:29:06.924   571-875   WindowManager           system_server                        E  No home screen found for Intent { act=android.intent.action.MAIN cat=[android.intent.category.HOME] flg=0x100 } (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.wm.RootWindowContainer.resolveHomeActivity(RootWindowContainer.java:1507)
                                                                                                    	at com.android.server.wm.RootWindowContainer.startHomeOnTaskDisplayArea(RootWindowContainer.java:1444)
                                                                                                    	at com.android.server.wm.RootWindowContainer.resumeHomeActivity(RootWindowContainer.java:1608)
                                                                                                    	at com.android.server.wm.Task.resumeNextFocusableActivityWhenRootTaskIsEmpty(Task.java:5101)
                                                                                                    	at com.android.server.wm.Task.resumeTopActivityInnerLocked(Task.java:5056)
                                                                                                    	at com.android.server.wm.Task.resumeTopActivityUncheckedLocked(Task.java:4991)
                                                                                                    	at com.android.server.wm.Task.resumeTopActivityUncheckedLocked(Task.java:5042)
                                                                                                    	at com.android.server.wm.RootWindowContainer.resumeFocusedTasksTopActivities(RootWindowContainer.java:2302)
                                                                                                    	at com.android.server.wm.ActivityStarter.startActivityInner(ActivityStarter.java:1698)
                                                                                                    	at com.android.server.wm.ActivityStarter.startActivityUnchecked(ActivityStarter.java:1398)
                                                                                                    	at com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:1227)
                                                                                                    	at com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:703)
                                                                                                    	at com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1293)
                                                                                                    	at com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1234)
                                                                                                    	at com.android.server.wm.ActivityTaskManagerService.startActivity(ActivityTaskManagerService.java:1209)
                                                                                                    	at android.app.IActivityTaskManager$Stub.onTransact(IActivityTaskManager.java:896)
                                                                                                    	at com.android.server.wm.ActivityTaskManagerService.onTransact(ActivityTaskManagerService.java:5350)
                                                                                                    	at android.os.Binder.execTransactInternal(Binder.java:1280)
                                                                                                    	at android.os.Binder.execTransact(Binder.java:1244)
2025-05-24 12:29:07.431   571-601   SystemServiceManager    system_server                        E  Skipping starting system user twice
2025-05-24 12:29:07.739   571-1081  WindowManager           system_server                        E  No home screen found for Intent { act=android.intent.action.MAIN cat=[android.intent.category.HOME] flg=0x100 } (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.wm.RootWindowContainer.resolveHomeActivity(RootWindowContainer.java:1507)
                                                                                                    	at com.android.server.wm.RootWindowContainer.startHomeOnTaskDisplayArea(RootWindowContainer.java:1444)
                                                                                                    	at com.android.server.wm.RootWindowContainer.resumeHomeActivity(RootWindowContainer.java:1608)
                                                                                                    	at com.android.server.wm.Task.resumeNextFocusableActivityWhenRootTaskIsEmpty(Task.java:5101)
                                                                                                    	at com.android.server.wm.Task.resumeTopActivityInnerLocked(Task.java:5056)
                                                                                                    	at com.android.server.wm.Task.resumeTopActivityUncheckedLocked(Task.java:4991)
                                                                                                    	at com.android.server.wm.Task.resumeTopActivityUncheckedLocked(Task.java:5042)
                                                                                                    	at com.android.server.wm.RootWindowContainer.resumeFocusedTasksTopActivities(RootWindowContainer.java:2302)
                                                                                                    	at com.android.server.wm.ActivityStarter.startActivityInner(ActivityStarter.java:1698)
                                                                                                    	at com.android.server.wm.ActivityStarter.startActivityUnchecked(ActivityStarter.java:1398)
                                                                                                    	at com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:1227)
                                                                                                    	at com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:703)
                                                                                                    	at com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1293)
                                                                                                    	at com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1234)
                                                                                                    	at android.app.IActivityTaskManager$Stub.onTransact(IActivityTaskManager.java:952)
                                                                                                    	at com.android.server.wm.ActivityTaskManagerService.onTransact(ActivityTaskManagerService.java:5350)
                                                                                                    	at android.os.Binder.execTransactInternal(Binder.java:1280)
                                                                                                    	at android.os.Binder.execTransact(Binder.java:1244)
2025-05-24 12:29:09.909   571-588   WindowManager           system_server                        E  setOnBackInvokedCallback(): No window state for package:com.android.car.cluster.home
2025-05-24 12:29:09.938   571-929   WindowManager           system_server                        E  setOnBackInvokedCallback(): No window state for package:com.android.car.settings
2025-05-24 12:29:10.733   571-929   WindowManager           system_server                        E  setOnBackInvokedCallback(): No window state for package:com.android.car.cluster.osdouble
2025-05-24 12:29:11.074   571-601   ActivityManager         system_server                        E  User switch timeout: from 0 to 10
2025-05-24 12:29:11.313   571-571   AutofillMa...erviceImpl system_server                        E  Bad service name: com.google.android.gms/.autofill.service.AutofillService
2025-05-24 12:29:11.314   571-571   AutofillMa...erviceImpl system_server                        E  Bad service name: com.google.android.gms/.autofill.service.AutofillService
2025-05-24 12:29:11.320   571-571   AutofillMa...erviceImpl system_server                        E  Bad service name: com.google.android.gms/.autofill.service.AutofillService
2025-05-24 12:29:12.513   571-693   NetworkPolicy           system_server                        E  Missing subscriberId for subId 1
2025-05-24 12:29:12.519   571-600   StatsPullAtomService    system_server                        E  subInfo of subId 1 is invalid, ignored.
2025-05-24 12:29:12.632   571-693   NetworkPolicy           system_server                        E  Missing subscriberId for subId 1
2025-05-24 12:29:12.635   571-600   StatsPullAtomService    system_server                        E  subInfo of subId 1 is invalid, ignored.
2025-05-24 12:29:14.603   571-739   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.604   571-739   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.606   571-739   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.606   571-739   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.607   571-740   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.607   571-740   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.608   571-740   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.608   571-740   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.612   571-593   TestHarnessModeService  system_server                        E  Failed to start Test Harness Mode; no implementation of PersistentDataBlockManagerInternal was bound!
2025-05-24 12:29:14.620   571-740   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.620   571-740   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.621   571-739   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.621   571-739   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.627   571-593   SystemServiceRegistry   system_server                        E  Manager wrapper not available: contexthub
2025-05-24 12:29:14.642   571-739   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.642   571-739   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.642   571-740   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.642   571-740   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.655   571-740   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.656   571-740   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.656   571-739   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:14.656   571-739   DisplayDeviceConfig     system_server                        E  requesting nits when no mapping exists.
2025-05-24 12:29:15.226   571-588   ActivityManager         system_server                        E  User switch timeout: observer #1 UserTrackerImpl's result was received 5344 ms after dispatchUserSwitch.
2025-05-24 12:29:15.506   571-601   SystemServiceManager    system_server                        E  SystemService failure: Failure reporting Unlocking of user 0 to service com.android.server.appsearch.AppSearchModule$Lifecycle (Ask Gemini)
                                                                                                    java.lang.SecurityException: Failed to find provider com.android.contacts for user 0; expected to find a valid ContentProvider for this authority
                                                                                                    	at com.android.server.content.ContentService.registerContentObserver(ContentService.java:376)
                                                                                                    	at android.content.ContentResolver.registerContentObserver(ContentResolver.java:2712)
                                                                                                    	at android.content.ContentResolver.registerContentObserver(ContentResolver.java:2660)
                                                                                                    	at com.android.server.appsearch.contactsindexer.ContactsIndexerUserInstance.startAsync(ContactsIndexerUserInstance.java:11)
                                                                                                    	at com.android.server.appsearch.contactsindexer.ContactsIndexerManagerService.onUserUnlocking(ContactsIndexerManagerService.java:79)
                                                                                                    	at com.android.server.appsearch.AppSearchModule$Lifecycle.onUserUnlocking(AppSearchModule.java:9)
                                                                                                    	at com.android.server.SystemServiceManager.onUser(SystemServiceManager.java:529)
                                                                                                    	at com.android.server.SystemServiceManager.onUser(SystemServiceManager.java:472)
                                                                                                    	at com.android.server.SystemServiceManager.onUser(SystemServiceManager.java:466)
                                                                                                    	at com.android.server.SystemServiceManager.onUserUnlocking(SystemServiceManager.java:386)
                                                                                                    	at com.android.server.am.UserController.handleMessage(UserController.java:2901)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:102)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
                                                                                                    	at com.android.server.ServiceThread.run(ServiceThread.java:44)
2025-05-24 12:29:15.843   571-590   TaskPersister           system_server                        E  restoreTasksForUserLocked: Unable to list files from /data/system_ce/0/recent_tasks
--------- beginning of crash
2025-05-24 12:29:18.193   322-394   NetlinkEvent            netd                                 E  NetlinkEvent::FindParam(): Parameter 'INTERFACE' not found
2025-05-24 12:29:18.193   322-394   NetlinkEvent            netd                                 E  NetlinkEvent::FindParam(): Parameter 'STATE' not found
2025-05-24 12:29:18.193   322-394   NetlinkEvent            netd                                 E  NetlinkEvent::FindParam(): Parameter 'TIME_NS' not found
2025-05-24 12:29:18.193   322-394   NetlinkEvent            netd                                 E  NetlinkEvent::FindParam(): Parameter 'UID' not found
2025-05-24 12:29:18.781   571-590   TaskPersister           system_server                        E  restoreTasksForUserLocked: Unable to list files from /data/system_ce/10/recent_tasks
2025-05-24 12:29:18.941   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:29:18.941   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:29:18.941   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:29:18.941   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:29:18.965   769-837   CAR.Vendor...Controller com.android.car                      E  Failed to start or bind service VendorService{component=com.android.car.cartelemetryapp/.CarMetricsCollectorService, bind=BIND, trigger=ASAP, userScope=SYSTEM}
2025-05-24 12:29:19.166   769-837   CAR.Vendor...Controller com.android.car                      E  Failed to start or bind service VendorService{component=com.android.car.messenger/.MessengerService, bind=START_FOREGROUND, trigger=UNLOCKED, userScope=FOREGROUND}
2025-05-24 12:29:19.173   769-837   CAR.Vendor...Controller com.android.car                      E  Failed to start or bind service VendorService{component=com.android.car.custominput.sample/.SampleCustomInputService, bind=BIND, trigger=UNLOCKED, userScope=FOREGROUND}
2025-05-24 12:29:19.174   769-837   CAR.Vendor...Controller com.android.car                      E  Failed to start or bind service VendorService{component=com.android.car.castreceiver/.CastReceiverCompanionService, bind=BIND, trigger=UNLOCKED, userScope=FOREGROUND}
2025-05-24 12:29:19.174   769-837   CAR.Vendor...Controller com.android.car                      E  Failed to start or bind service VendorService{component=com.google.android.apps.mediashell/.automotive.AutomotiveCastReceiverService, bind=BIND, trigger=UNLOCKED, userScope=FOREGROUND}
2025-05-24 12:29:19.965   571-2106  ActivityThread          system_server                        E  Failed to find provider info for call_log
2025-05-24 12:29:20.712   571-571   ActivityThread          system_server                        E  Failed to find provider info for com.android.blockednumber
2025-05-24 12:29:23.661   571-600   PackageManager          system_server                        E  Optimistic bind failed. (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: [Optimistic Bind] Didn't bind to resolver in time!
                                                                                                    	at com.android.server.pm.InstantAppResolverConnection.waitForBindLocked(InstantAppResolverConnection.java:160)
                                                                                                    	at com.android.server.pm.InstantAppResolverConnection.bind(InstantAppResolverConnection.java:225)
                                                                                                    	at com.android.server.pm.InstantAppResolverConnection.lambda$optimisticBind$0$com-android-server-pm-InstantAppResolverConnection(InstantAppResolverConnection.java:255)
                                                                                                    	at com.android.server.pm.InstantAppResolverConnection$$ExternalSyntheticLambda0.run(Unknown Source:2)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-24 12:29:24.075  2078-2398  ActivityThread          android.process.acore                E  Failed to find provider info for call_log
--------- beginning of main
2025-05-24 12:29:27.363  2641-2641  moteprovisioner         com.android.remoteprovisioner        E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:28.021   337-337   WVCdm                   and...hardware.drm-service.widevine  E  [oemcrypto_adapter_dynamic.cpp(958):Level1Terminate] L1 Terminate not needed
2025-05-24 12:29:28.167  1669-2366  CCTFlatFileLogStore     com.google.android.gms.persistent    E  Failed to get boot count. (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@*********@24.26.32 (230800-*********):53)
                                                                                                    	at absp.B(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at abpf.run(:com.google.android.gms@*********@24.26.32 (230800-*********):13)
                                                                                                    	at adtm.c(:com.google.android.gms@*********@24.26.32 (230800-*********):50)
                                                                                                    	at adtm.run(:com.google.android.gms@*********@24.26.32 (230800-*********):97)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:29:28.310  2697-2697  m.android.shell         com.android.shell                    E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:28.411  1554-1890  OpenGLRenderer          com.android.systemui                 E  Unable to match the desired swap behavior.
2025-05-24 12:29:28.474  2226-2242  CarSetupWizard          com.google.android.car.setupwizard   E  [clf] Timeout reached before completing retrieval for PersistentFlags Waited 100 milliseconds (plus 793000 nanoseconds delay) for feb@e7ac73e[status=PENDING, info=[delegate=[fcq@aefe99f[status=PENDING, info=[inputFuture=[epp@14f1aec[status=PENDING, info=[callable=[eim@76a8eb5], trial=[epq@4d8194a[status=PENDING, setFuture=[ffh@f380bb[status=PENDING, setFuture=[fdg@488f9d8[status=PENDING, info=[futures=[epp@f826131[status=PENDING, info=[callable=[dxj@1068016], trial=[epq@1cf6d97[status=PENDING, setFuture=[ffh@27fdb84[status=PENDING, setFuture=[fcr@16adf6d[status=PENDING, info=[inputFuture=[fcq@5e287a2[status=PENDING, setFuture=[epp@134c33[status=PENDING, info=[callable=[dxj@be12bf0], trial=[epq@5e30569[status=PENDING, setFuture=[ffh@5857bee[status=PENDING, info=[task=[running=[RUNNING ON BG Thread #0], propagating=[dxj@be12bf0]]]]]]]]]]]], function=[Functions.constant(null)]]]]]]]]]]]]]]]]]]]]], function=[propagating=[drw@dac788f]]]]]]]
2025-05-24 12:29:28.528  2504-2653  Finsky                  com.android.vending                  E  [181] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:29:28.607  1554-1890  OpenGLRenderer          com.android.systemui                 E  Unable to match the desired swap behavior.
2025-05-24 12:29:29.335  2504-2659  Finsky                  com.android.vending                  E  [183] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-24 12:29:29.336  2504-2659  Finsky                  com.android.vending                  E  [183] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:29:29.595  2779-2779  ive.inputmethod         com...d.apps.automotive.inputmethod  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:29.714  2792-2792  e.process.gapps         com.google.process.gapps             E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:29.989  2616-2616  droid.bluetooth         pid-2616                             E  [0524/042929.988998:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb7cde53b9
2025-05-24 12:29:29.989  2616-2616  droid.bluetooth         pid-2616                             E  [0524/042929.989134:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb7cde53b9
2025-05-24 12:29:29.989  2616-2616  droid.bluetooth         pid-2616                             E  [0524/042929.989180:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb7cde53b9
2025-05-24 12:29:29.989  2616-2616  droid.bluetooth         pid-2616                             E  [0524/042929.989215:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb7cde53b9
2025-05-24 12:29:29.989  2616-2616  droid.bluetooth         pid-2616                             E  [0524/042929.989249:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb7cde53b9
2025-05-24 12:29:29.989  2616-2616  droid.bluetooth         pid-2616                             E  [0524/042929.989287:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb7cde53b9
2025-05-24 12:29:30.119  2830-2830  android.vending         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:30.271  2867-2867  tatementservice         com.android.statementservice         E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:31.162  2616-2616  droid.bluetooth         pid-2616                             E  [0524/042931.162667:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb7cded935
2025-05-24 12:29:31.162  2616-2616  droid.bluetooth         pid-2616                             E  [0524/042931.162708:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb7cded935
2025-05-24 12:29:31.162  2616-2616  droid.bluetooth         pid-2616                             E  [0524/042931.162739:ERROR:bta_sys_main.cc(171)] bta_sys_sendmsg: do_in_main_thread failed
2025-05-24 12:29:31.239  2902-2902  ding:background         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:31.273  2195-2448  tflite                  com.google.android.tts               E  third_party/tensorflow/lite/core/subgraph.cc:1059 tensor.data.raw != nullptr was not true.
2025-05-24 12:29:31.497  2934-2934  id.gms.unstable         com.google.android.gms.unstable      E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:31.577  1447-1927  UsageReportingOptionsSt com.google.android.gms.persistent    E  INTERNAL_ERROR: setOptInOption should not be called while user is locked. [CONTEXT service_id=41 ]
2025-05-24 12:29:31.755  2830-2893  Finsky                  com.android.vending                  E  [202] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:29:32.057  2830-2895  Finsky                  com.android.vending                  E  [203] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-24 12:29:32.058  2830-2895  Finsky                  com.android.vending                  E  [203] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:29:32.188  2830-2966  Finsky                  com.android.vending                  E  [221] tvv.l(6): Attempting to set verify apps consent in a secondary user profile
2025-05-24 12:29:32.259  2830-2966  Finsky                  com.android.vending                  E  [221] rcz.WA(109): VerifyApps: Error occurred while updating consent (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Can't update consent in secondary user profile
                                                                                                    	at tvv.l(PG:13)
                                                                                                    	at tcv.q(PG:1)
                                                                                                    	at tvy.f(PG:1)
                                                                                                    	at twf.b(PG:418)
                                                                                                    	at twf.i(PG:16)
                                                                                                    	at pze.apply(PG:147)
                                                                                                    	at ablc.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:29:32.260  2830-2966  Finsky                  com.android.vending                  E  [221] tvv.g(6): Attempting to set verify apps consent default-on flag in a secondary user profile
2025-05-24 12:29:32.335  3001-3001  DEBUG                   pid-3001                             E  failed to read process info: failed to open /proc/2616
2025-05-24 12:29:32.360  3012-3012  externalstorage         com.android.externalstorage          E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:32.645  2641-2657  DrmHalHidl              com.android.remoteprovisioner        E  Failed to get vendor from drm plugin: -19
2025-05-24 12:29:32.645  2641-2657  DrmHalHidl              com.android.remoteprovisioner        E  Failed to get description from drm plugin: -19
2025-05-24 12:29:33.330   233-233   tombstoned              tombstoned                           E  Tombstone written to: tombstone_00
2025-05-24 12:29:33.353   571-648   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-24 12:29:33.375  3053-3053  d.configupdater         com.google.android.configupdater     E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:33.569   571-664   BluetoothManagerService system_server                        E  MESSAGE_BLUETOOTH_SERVICE_DISCONNECTED(1)
2025-05-24 12:29:33.592   571-1191  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 12:29:33.705  3105-3105  ding:background         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:34.014  3145-3145  externalstorage         com.android.externalstorage          E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:34.061  3053-3053  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:34.271  2504-2720  Finsky                  com.android.vending                  E  [201] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:29:34.271  2504-2720  Finsky                  com.android.vending                  E  [201] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:29:34.271  2504-2720  Finsky                  com.android.vending                  E  [201] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:29:34.279  2504-2659  Finsky                  com.android.vending                  E  [183] kzd.run(1284): Upload device configuration failed
2025-05-24 12:29:34.282  3053-3053  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:34.375  3053-3053  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:34.410  3053-3053  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:34.429  3053-3053  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:34.465  3053-3053  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:34.485   571-648   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-24 12:29:34.495  3053-3053  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:34.607  2504-2659  Finsky                  com.android.vending                  E  [183] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:29:34.683  3208-3208  droid.bluetooth         pid-3208                             E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:34.686  3053-3053  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:34.772  3053-3053  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:34.781   571-695   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-24 12:29:34.800  3053-3053  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:34.864  3053-3053  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:34.893  2504-2659  Finsky                  com.android.vending                  E  [183] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-24 12:29:34.966  3053-3053  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:35.152  1757-3297  PhenotypeRegOp          com.google.android.gms               E  Attempting to overwrite config package for com.google.android.gms.auth_account_auto#com.google.android.gms [CONTEXT service_id=231 ]
2025-05-24 12:29:35.164  3305-3305  ackageinstaller         com.google.android.packageinstaller  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:35.260  3322-3322  d.process.media         android.process.media                E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:35.319  1669-1911  UsageReportingOptionsSt com.google.android.gms.persistent    E  INTERNAL_ERROR: setOptInOption should not be called while user is locked. [CONTEXT service_id=41 ]
2025-05-24 12:29:35.372  2830-2966  Finsky                  com.android.vending                  E  [221] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:29:35.373  2830-2966  Finsky                  com.android.vending                  E  [221] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:29:35.383  2830-2966  Finsky                  com.android.vending                  E  [221] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:29:35.385  2830-2895  Finsky                  com.android.vending                  E  [203] kzd.run(1284): Upload device configuration failed
2025-05-24 12:29:35.617  3368-3368  id.gms.unstable         com.google.android.gms.unstable      E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:35.734  2830-2895  Finsky                  com.android.vending                  E  [203] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:29:35.776  2830-2895  Finsky                  com.android.vending                  E  [203] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-24 12:29:35.977  1333-1516  native                  com.google.android.carassistant      E  E0000 00:00:**********.977249    1516 soda_client.cc:674] Cannot setup DataProvider due to missing instance.
2025-05-24 12:29:36.021  1333-2024  native                  com.google.android.carassistant      E  E0000 00:00:**********.021887    2024 soda_client.cc:674] Cannot setup DataProvider due to missing instance.
2025-05-24 12:29:36.047  3416-3416  ssioncontroller         com....android.permissioncontroller  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:36.208  3208-3208  droid.bluetooth         pid-3208                             E  [0524/042936.208775:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb7f3e43b9
2025-05-24 12:29:36.208  3208-3208  droid.bluetooth         pid-3208                             E  [0524/042936.208813:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb7f3e43b9
2025-05-24 12:29:36.208  3208-3208  droid.bluetooth         pid-3208                             E  [0524/042936.208836:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb7f3e43b9
2025-05-24 12:29:36.208  3208-3208  droid.bluetooth         pid-3208                             E  [0524/042936.208857:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb7f3e43b9
2025-05-24 12:29:36.208  3208-3208  droid.bluetooth         pid-3208                             E  [0524/042936.208878:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb7f3e43b9
2025-05-24 12:29:36.208  3208-3208  droid.bluetooth         pid-3208                             E  [0524/042936.208900:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb7f3e43b9
2025-05-24 12:29:36.550  1447-2185  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-24 12:29:36.864  1333-1516  SpeechMicro             com.google.android.carassistant      E  Hotword model is single-channel neuralnet.
2025-05-24 12:29:36.871  1333-2024  SpeechMicro             com.google.android.carassistant      E  Hotword model is single-channel neuralnet.
2025-05-24 12:29:36.907  1333-1516  SpeechMicro             com.google.android.carassistant      E  Hotword model is single-channel neuralnet.
2025-05-24 12:29:36.983  3208-3208  droid.bluetooth         pid-3208                             E  [0524/042936.983388:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb7f3ec935
2025-05-24 12:29:36.983  3208-3208  droid.bluetooth         pid-3208                             E  [0524/042936.983415:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb7f3ec935
2025-05-24 12:29:36.983  3208-3208  droid.bluetooth         pid-3208                             E  [0524/042936.983430:ERROR:bta_sys_main.cc(171)] bta_sys_sendmsg: do_in_main_thread failed
2025-05-24 12:29:37.016  1333-2024  SpeechMicro             com.google.android.carassistant      E  Hotword model is single-channel neuralnet.
2025-05-24 12:29:37.303  3554-3554  roid.car.dialer         com.android.car.dialer               E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:37.651  1333-2025  native                  com.google.android.carassistant      E  E0000 00:00:**********.635144    2025 soda_client.cc:674] Cannot setup DataProvider due to missing instance.
2025-05-24 12:29:37.788  3581-3581  DEBUG                   pid-3581                             E  failed to read process info: failed to open /proc/3208
2025-05-24 12:29:37.797  1333-2025  SpeechMicro             com.google.android.carassistant      E  Hotword model is single-channel neuralnet.
2025-05-24 12:29:37.804  1333-2025  SpeechMicro             com.google.android.carassistant      E  Hotword model is single-channel neuralnet.
2025-05-24 12:29:37.907  3618-3618  d.car.messenger         com.android.car.messenger            E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:38.300   233-233   tombstoned              tombstoned                           E  Tombstone written to: tombstone_01
2025-05-24 12:29:38.370   571-648   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-24 12:29:38.386  2779-3680  Handwritin...rpacksUtil com...d.apps.automotive.inputmethod  E  HandwritingSuperpacksUtil.getPackMappingPackName():40 No pack mapping pack found in []
2025-05-24 12:29:38.398  3669-3669  ndroid.contacts         com.android.contacts                 E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:38.485   571-664   BluetoothManagerService system_server                        E  MESSAGE_BLUETOOTH_SERVICE_DISCONNECTED(1)
2025-05-24 12:29:38.510   571-648   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-24 12:29:38.529   571-1294  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 12:29:38.780  2779-3679  Handwritin...rpacksUtil com...d.apps.automotive.inputmethod  E  HandwritingSuperpacksUtil.getPackMappingPackName():40 No pack mapping pack found in []
2025-05-24 12:29:39.038  3716-3716  droid.dynsystem         com.android.dynsystem                E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:39.424  2153-2955  OpenGLRenderer          com.google.android.apps.maps         E  Unable to match the desired swap behavior.
2025-05-24 12:29:39.542  1757-3297  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.mg.ui.main.MainActivity' is not available.
2025-05-24 12:29:39.542  1757-3297  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.SettingsLoaderActivity' is not available.
2025-05-24 12:29:39.543  1757-3297  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.service.PurgeScreenDataService' is not available.
2025-05-24 12:29:39.543  1757-3297  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.ZeroPartyEntryPointActivity' is not available.
2025-05-24 12:29:39.555  1757-3298  adservices              com.google.android.gms               E  Exception caught when modifying AdExtDtaService state! [CONTEXT service_id=261 ] (Ask Gemini)
                                                                                                    java.lang.IllegalArgumentException: Component class com.google.android.gms.adsidentity.service.AdServicesExtDataStorageService does not exist in com.google.android.gms
                                                                                                    	at android.os.Parcel.createExceptionOrNull(Parcel.java:3015)
                                                                                                    	at android.os.Parcel.createException(Parcel.java:2995)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2978)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2920)
                                                                                                    	at android.content.pm.IPackageManager$Stub$Proxy.setComponentEnabledSetting(IPackageManager.java:5881)
                                                                                                    	at android.app.ApplicationPackageManager.setComponentEnabledSetting(ApplicationPackageManager.java:3050)
                                                                                                    	at adui.m(:com.google.android.gms@*********@24.26.32 (230800-*********):30)
                                                                                                    	at aduj.M(:com.google.android.gms@*********@24.26.32 (230800-*********):12)
                                                                                                    	at com.google.android.gms.adsidentity.init.ModifyAdServicesExtDataStorageServiceStateIntentOperation.d(:com.google.android.gms@*********@24.26.32 (230800-*********):11)
                                                                                                    	at com.google.android.gms.adsidentity.init.ModifyAdServicesExtDataStorageServiceStateIntentOperation.b(:com.google.android.gms@*********@24.26.32 (230800-*********):1)
                                                                                                    	at zyx.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):136)
                                                                                                    	at com.google.android.gms.adsidentity.init.ModifyAdServicesExtDataStorageServiceStateIntentOperation.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):22)
                                                                                                    	at com.google.android.chimera.IntentOperation.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):2)
                                                                                                    	at aaef.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at lnq.run(:com.google.android.gms@*********@24.26.32 (230800-*********):70)
                                                                                                    	at lnp.run(:com.google.android.gms@*********@24.26.32 (230800-*********):152)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:29:39.582  3748-3748  gedprovisioning         com.android.managedprovisioning      E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:39.641  1757-3297  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.PrivacyHubActivityControlsActivity' is not available.
2025-05-24 12:29:39.641  1757-3297  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.SafetyCenterActivityControlsActivity' is not available.
2025-05-24 12:29:39.682  3770-3770  droid.bluetooth         pid-3770                             E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:39.922  1447-1737  bnbi                    com.google.android.gms.persistent    E  Phenotype API error. Event: # ddtm@cab8acfb, EventCode: GET_STORAGE_INFO [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    bmzp: 29514: Storage info not created for GMS or Play Store.
                                                                                                    	at bndq.b(:com.google.android.gms@*********@24.26.32 (230800-*********):807)
                                                                                                    	at bncl.i(:com.google.android.gms@*********@24.26.32 (230800-*********):13)
                                                                                                    	at bnbi.h(:com.google.android.gms@*********@24.26.32 (230800-*********):18)
                                                                                                    	at bnbi.f(:com.google.android.gms@*********@24.26.32 (230800-*********):11)
                                                                                                    	at auvg.eV(:com.google.android.gms@*********@24.26.32 (230800-*********):1)
                                                                                                    	at auvl.run(:com.google.android.gms@*********@24.26.32 (230800-*********):132)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at adtm.c(:com.google.android.gms@*********@24.26.32 (230800-*********):50)
                                                                                                    	at adtm.run(:com.google.android.gms@*********@24.26.32 (230800-*********):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:29:40.086  3799-3799  m.android.shell         com.android.shell                    E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:40.117  1757-3297  JavaBinder              com.google.android.gms               E  !!! FAILED BINDER TRANSACTION !!!  (parcel size = 1056768)
2025-05-24 12:29:40.253  3823-3823  tatementservice         com.android.statementservice         E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:40.807   322-404   BpfHandler              netd                                 E  Unsupported protocol: 1
2025-05-24 12:29:41.088  3770-3770  droid.bluetooth         pid-3770                             E  [0524/042941.088679:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb819e43b9
2025-05-24 12:29:41.088  3770-3770  droid.bluetooth         pid-3770                             E  [0524/042941.088804:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb819e43b9
2025-05-24 12:29:41.088  3770-3770  droid.bluetooth         pid-3770                             E  [0524/042941.088829:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb819e43b9
2025-05-24 12:29:41.088  3770-3770  droid.bluetooth         pid-3770                             E  [0524/042941.088847:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb819e43b9
2025-05-24 12:29:41.088  3770-3770  droid.bluetooth         pid-3770                             E  [0524/042941.088863:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb819e43b9
2025-05-24 12:29:41.088  3770-3770  droid.bluetooth         pid-3770                             E  [0524/042941.088879:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb819e43b9
2025-05-24 12:29:41.321  1447-3772  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-24 12:29:41.707  3770-3770  droid.bluetooth         pid-3770                             E  [0524/042941.707215:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb819ec935
2025-05-24 12:29:41.707  3770-3770  droid.bluetooth         pid-3770                             E  [0524/042941.707269:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb819ec935
2025-05-24 12:29:41.707  3770-3770  droid.bluetooth         pid-3770                             E  [0524/042941.707315:ERROR:bta_sys_main.cc(171)] bta_sys_sendmsg: do_in_main_thread failed
2025-05-24 12:29:42.605  3941-3941  DEBUG                   pid-3941                             E  failed to read process info: failed to open /proc/3770
2025-05-24 12:29:43.467   233-233   tombstoned              tombstoned                           E  Tombstone written to: tombstone_02
2025-05-24 12:29:43.469   571-648   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-24 12:29:43.503  2504-2694  Finsky                  com.android.vending                  E  [195] gjj.accept(91): GMS compliance query failed.
                                                                                                    com.google.common.util.concurrent.TimeoutFuture$TimeoutFutureException: Timed out: ablb@24e0fe2[status=PENDING, setFuture=[dqd@edfcf73[status=PENDING, info=[tag=[vxg@c728e30]]]]]
2025-05-24 12:29:43.625   571-664   BluetoothManagerService system_server                        E  MESSAGE_BLUETOOTH_SERVICE_DISCONNECTED(1)
2025-05-24 12:29:43.682   571-648   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-24 12:29:43.869  1447-1802  PhenotypeFlagCommitter  com.google.android.gms.persistent    E  Retrieving snapshot for com.google.android.gms.lockbox failed (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@*********@24.26.32 (230800-*********):53)
                                                                                                    	at bmyl.i(:com.google.android.gms@*********@24.26.32 (230800-*********):13)
                                                                                                    	at bmyl.j(:com.google.android.gms@*********@24.26.32 (230800-*********):42)
                                                                                                    	at bmyl.h(:com.google.android.gms@*********@24.26.32 (230800-*********):4)
                                                                                                    	at com.google.android.gms.lockbox.LockboxIntentOperation.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):48)
                                                                                                    	at com.google.android.chimera.IntentOperation.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):2)
                                                                                                    	at aaef.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at lnq.run(:com.google.android.gms@*********@24.26.32 (230800-*********):70)
                                                                                                    	at lnp.run(:com.google.android.gms@*********@24.26.32 (230800-*********):152)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:29:44.758  4003-4003  droid.bluetooth         pid-4003                             E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:45.506  4003-4003  droid.bluetooth         pid-4003                             E  [0524/042945.506673:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb825e53b9
2025-05-24 12:29:45.506  4003-4003  droid.bluetooth         pid-4003                             E  [0524/042945.506750:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb825e53b9
2025-05-24 12:29:45.506  4003-4003  droid.bluetooth         pid-4003                             E  [0524/042945.506790:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb825e53b9
2025-05-24 12:29:45.506  4003-4003  droid.bluetooth         pid-4003                             E  [0524/042945.506820:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb825e53b9
2025-05-24 12:29:45.506  4003-4003  droid.bluetooth         pid-4003                             E  [0524/042945.506836:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb825e53b9
2025-05-24 12:29:45.506  4003-4003  droid.bluetooth         pid-4003                             E  [0524/042945.506858:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb825e53b9
2025-05-24 12:29:45.889  4003-4003  droid.bluetooth         pid-4003                             E  [0524/042945.889177:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb825ed935
2025-05-24 12:29:45.889  4003-4003  droid.bluetooth         pid-4003                             E  [0524/042945.889247:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb825ed935
2025-05-24 12:29:45.889  4003-4003  droid.bluetooth         pid-4003                             E  [0524/042945.889904:ERROR:bta_sys_main.cc(171)] bta_sys_sendmsg: do_in_main_thread failed
2025-05-24 12:29:46.608  4088-4088  DEBUG                   pid-4088                             E  failed to read process info: failed to open /proc/4003
2025-05-24 12:29:46.656  1447-2178  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-24 12:29:46.735  2830-2966  Finsky                  com.android.vending                  E  [221] gjj.accept(91): GMS compliance query failed.
                                                                                                    com.google.common.util.concurrent.TimeoutFuture$TimeoutFutureException: Timed out: ablb@260b5e1[status=PENDING, setFuture=[dqd@79e2f06[status=PENDING, info=[tag=[vxg@55209c7]]]]]
2025-05-24 12:29:46.882   233-233   tombstoned              tombstoned                           E  Tombstone written to: tombstone_03
2025-05-24 12:29:46.882   571-648   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-24 12:29:46.883   571-648   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-24 12:29:46.961   571-664   BluetoothManagerService system_server                        E  MESSAGE_BLUETOOTH_SERVICE_DISCONNECTED(1)
2025-05-24 12:29:47.817  2133-4110  chromium                com.google.android.carassistant      E  [0524/042947.785819:ERROR:variations_seed_loader.cc(37)] Seed missing signature.
2025-05-24 12:29:48.033  4120-4120  droid.bluetooth         pid-4120                             E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:48.464  4149-4149  d.configupdater         com.google.android.configupdater     E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:48.648  4120-4120  droid.bluetooth         pid-4120                             E  [0524/042948.648933:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb811e53b9
2025-05-24 12:29:48.649  4120-4120  droid.bluetooth         pid-4120                             E  [0524/042948.649001:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb811e53b9
2025-05-24 12:29:48.649  4120-4120  droid.bluetooth         pid-4120                             E  [0524/042948.649040:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb811e53b9
2025-05-24 12:29:48.649  4120-4120  droid.bluetooth         pid-4120                             E  [0524/042948.649090:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb811e53b9
2025-05-24 12:29:48.649  4120-4120  droid.bluetooth         pid-4120                             E  [0524/042948.649150:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb811e53b9
2025-05-24 12:29:48.649  4120-4120  droid.bluetooth         pid-4120                             E  [0524/042948.649190:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb811e53b9
2025-05-24 12:29:48.655  4149-4149  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:48.669  4149-4149  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:48.690  4149-4149  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:48.696  4149-4149  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:48.704  4149-4149  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:48.711  4149-4149  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:48.716  4149-4149  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:48.779  4149-4149  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:48.805  4149-4149  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:48.846  4149-4149  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:48.876  4149-4149  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:48.879  4149-4149  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-24 12:29:49.112  4226-4226  timeinitializer         com...le.android.onetimeinitializer  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:49.177  1950-4228  PhenotypeRegOp          com.google.android.gms               E  Attempting to overwrite config package for com.google.android.gms.auth_account_auto#com.google.android.gms [CONTEXT service_id=231 ]
2025-05-24 12:29:49.314  4120-4120  droid.bluetooth         pid-4120                             E  [0524/042949.314627:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x74cb811ed935
2025-05-24 12:29:49.314  4120-4120  droid.bluetooth         pid-4120                             E  [0524/042949.314688:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x74cb811ed935
2025-05-24 12:29:49.314  4120-4120  droid.bluetooth         pid-4120                             E  [0524/042949.314736:ERROR:bta_sys_main.cc(171)] bta_sys_sendmsg: do_in_main_thread failed
2025-05-24 12:29:49.928  4277-4277  ackageinstaller         com.google.android.packageinstaller  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:50.309  4301-4301  id.partnersetup         com.google.android.partnersetup      E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:50.439  4300-4300  DEBUG                   pid-4300                             E  failed to read process info: failed to open /proc/4120
2025-05-24 12:29:52.175   571-664   BluetoothManagerService system_server                        E  waitForState [12] time out
2025-05-24 12:29:52.676   571-695   WifiHealthMonitor       system_server                        E   Hit PackageManager exception (Ask Gemini)
                                                                                                    android.content.pm.PackageManager$NameNotFoundException: No module info for package: com.android.wifi
                                                                                                    	at android.app.ApplicationPackageManager.getModuleInfo(ApplicationPackageManager.java:1187)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.getWifiStackVersion(WifiHealthMonitor.java:366)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.extractCurrentSoftwareBuildInfo(WifiHealthMonitor.java:587)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.postBootSwBuildCheck(WifiHealthMonitor.java:522)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.postBootDetectionHandler(WifiHealthMonitor.java:513)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.-$$Nest$mpostBootDetectionHandler(Unknown Source:0)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor$2.onAlarm(WifiHealthMonitor.java:190)
                                                                                                    	at android.app.AlarmManager$ListenerWrapper.run(AlarmManager.java:357)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-24 12:29:53.074  4120-4169  AdapterState            pid-4120                             E  BLE_TURNING_ON : BLE_START_TIMEOUT
2025-05-24 12:29:53.757   233-233   tombstoned              tombstoned                           E  Tombstone written to: tombstone_04
2025-05-24 12:29:53.764   571-648   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-24 12:29:53.765   571-648   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-24 12:29:54.136   571-664   BluetoothManagerService system_server                        E  MESSAGE_BLUETOOTH_SERVICE_DISCONNECTED(1)
2025-05-24 12:29:54.970  1950-4228  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.mg.ui.main.MainActivity' is not available.
2025-05-24 12:29:54.977  1950-4228  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.SettingsLoaderActivity' is not available.
2025-05-24 12:29:54.980  1950-4228  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.service.PurgeScreenDataService' is not available.
2025-05-24 12:29:54.981  1950-4228  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.ZeroPartyEntryPointActivity' is not available.
2025-05-24 12:29:55.061  1950-4325  adservices              com.google.android.gms               E  Exception caught when modifying AdExtDtaService state! [CONTEXT service_id=261 ] (Ask Gemini)
                                                                                                    java.lang.IllegalArgumentException: Component class com.google.android.gms.adsidentity.service.AdServicesExtDataStorageService does not exist in com.google.android.gms
                                                                                                    	at android.os.Parcel.createExceptionOrNull(Parcel.java:3015)
                                                                                                    	at android.os.Parcel.createException(Parcel.java:2995)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2978)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2920)
                                                                                                    	at android.content.pm.IPackageManager$Stub$Proxy.setComponentEnabledSetting(IPackageManager.java:5881)
                                                                                                    	at android.app.ApplicationPackageManager.setComponentEnabledSetting(ApplicationPackageManager.java:3050)
                                                                                                    	at adui.m(:com.google.android.gms@*********@24.26.32 (230800-*********):30)
                                                                                                    	at aduj.M(:com.google.android.gms@*********@24.26.32 (230800-*********):12)
                                                                                                    	at com.google.android.gms.adsidentity.init.ModifyAdServicesExtDataStorageServiceStateIntentOperation.d(:com.google.android.gms@*********@24.26.32 (230800-*********):11)
                                                                                                    	at com.google.android.gms.adsidentity.init.ModifyAdServicesExtDataStorageServiceStateIntentOperation.b(:com.google.android.gms@*********@24.26.32 (230800-*********):1)
                                                                                                    	at zyx.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):136)
                                                                                                    	at com.google.android.gms.adsidentity.init.ModifyAdServicesExtDataStorageServiceStateIntentOperation.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):22)
                                                                                                    	at com.google.android.chimera.IntentOperation.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):2)
                                                                                                    	at aaef.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at lnq.run(:com.google.android.gms@*********@24.26.32 (230800-*********):70)
                                                                                                    	at lnp.run(:com.google.android.gms@*********@24.26.32 (230800-*********):152)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:29:55.149  1950-4228  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.PrivacyHubActivityControlsActivity' is not available.
2025-05-24 12:29:55.150  1950-4228  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.SafetyCenterActivityControlsActivity' is not available.
2025-05-24 12:29:55.716  1669-2444  bnbi                    com.google.android.gms.persistent    E  Phenotype API error. Event: # ddtm@cab8acfb, EventCode: GET_STORAGE_INFO [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    bmzp: 29514: Storage info not created for GMS or Play Store.
                                                                                                    	at bndq.b(:com.google.android.gms@*********@24.26.32 (230800-*********):807)
                                                                                                    	at bncl.i(:com.google.android.gms@*********@24.26.32 (230800-*********):13)
                                                                                                    	at bnbi.h(:com.google.android.gms@*********@24.26.32 (230800-*********):18)
                                                                                                    	at bnbi.f(:com.google.android.gms@*********@24.26.32 (230800-*********):11)
                                                                                                    	at auvg.eV(:com.google.android.gms@*********@24.26.32 (230800-*********):1)
                                                                                                    	at auvl.run(:com.google.android.gms@*********@24.26.32 (230800-*********):132)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at adtm.c(:com.google.android.gms@*********@24.26.32 (230800-*********):50)
                                                                                                    	at adtm.run(:com.google.android.gms@*********@24.26.32 (230800-*********):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:29:55.722  1950-4230  JavaBinder              com.google.android.gms               E  !!! FAILED BINDER TRANSACTION !!!  (parcel size = 1056768)
2025-05-24 12:29:56.280   322-404   BpfHandler              netd                                 E  Unsupported protocol: 1
2025-05-24 12:29:56.544   571-664   BluetoothManagerService system_server                        E  Reach maximum retry to restart Bluetooth!
2025-05-24 12:29:56.581  4384-4384  ssioncontroller         com....android.permissioncontroller  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:29:57.337  4301-4301  GooglePartnerSetup      com.google.android.partnersetup      E  Phenotype client.register: true
2025-05-24 12:30:02.002  2504-2714  Finsky                  com.android.vending                  E  [197] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:30:02.007  2504-2714  Finsky                  com.android.vending                  E  [197] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:30:02.008  2504-2714  Finsky                  com.android.vending                  E  [197] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:30:02.097  2504-2769  Finsky                  com.android.vending                  E  [220] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:30:03.622  1757-3760  WorkSourceUtil          com.google.android.gms               E  Could not find package: com.google.android.gms.westworld
2025-05-24 12:30:05.255  2830-2960  Finsky                  com.android.vending                  E  [217] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:30:05.257  2830-2960  Finsky                  com.android.vending                  E  [217] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:30:05.258  2830-2960  Finsky                  com.android.vending                  E  [217] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:30:05.325  2830-3003  Finsky                  com.android.vending                  E  [240] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:30:06.686  1447-4491  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-24 12:30:10.492  2504-2769  Finsky                  com.android.vending                  E  [220] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:30:13.724  2830-3003  Finsky                  com.android.vending                  E  [240] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:30:14.794   571-695   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-24 12:30:21.069   571-633   CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 12:30:21.230  4578-4578  car.carlauncher         com.android.car.carlauncher          E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:30:23.134   571-4098  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 12:30:23.155  4578-4578  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-24 12:30:23.431  4578-4648  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 12:30:23.926  4578-4578  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-24 12:30:23.953  4578-4648  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 12:30:24.030  4578-4648  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 12:30:25.042  2153-2153  droid.apps.maps         com.google.android.apps.maps         E  No package ID ff found for ID 0xffffffff.
2025-05-24 12:30:25.176  4578-4578  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-24 12:30:25.197  2153-2955  OpenGLRenderer          com.google.android.apps.maps         E  Unable to match the desired swap behavior.
2025-05-24 12:30:25.232  4724-4724  ng:quick_launch         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:30:25.801  4771-4771  ng:quick_launch         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 12:30:27.671  2830-2960  Finsky                  com.android.vending                  E  [217] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:30:27.675  2830-2960  Finsky                  com.android.vending                  E  [217] rcz.WA(261): [PLUS] Failed to trigger NOW urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:30:27.685  2830-2960  Finsky                  com.android.vending                  E  [217] obb.a(333): SCH: Job 37-3 threw exception (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:30:28.129   571-862   CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 12:30:28.221   571-3688  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 12:30:28.283  2504-2714  Finsky                  com.android.vending                  E  [197] iuw.a(52): Unexpected android-id = 0
2025-05-24 12:30:28.284  2504-2714  Finsky                  com.android.vending                  E  [197] rcz.WA(261): [PLUS] Failed to trigger NOW urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:30:28.298  2504-2714  Finsky                  com.android.vending                  E  [197] obb.a(333): SCH: Job 37-3 threw exception (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:30:28.393  4578-4648  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 12:30:28.740  4578-4648  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 12:30:31.204   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:30:31.204   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:30:31.205   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:30:31.421   571-624   PackageManager          system_server                        E  Backup Manager not found!
2025-05-24 12:30:31.427   432-455   installd                installd                             E  Couldn't opendir /data/app/vmdl479576287.tmp: No such file or directory
2025-05-24 12:30:31.427   432-455   installd                installd                             E  Failed to delete /data/app/vmdl479576287.tmp: No such file or directory
2025-05-24 12:30:31.635   947-947   SmsApplication          com.android.phone                    E  com.android.mms.service does not have system signature
2025-05-24 12:30:31.999  3416-4869  BackupHelper            com....android.permissioncontroller  E  Could not parse delayed permissions (Ask Gemini)
                                                                                                    java.io.FileNotFoundException: /data/user_de/10/com.google.android.permissioncontroller/files/delayed_restore_permissions.xml: open failed: ENOENT (No such file or directory)
                                                                                                    	at libcore.io.IoBridge.open(IoBridge.java:574)
                                                                                                    	at java.io.FileInputStream.<init>(FileInputStream.java:160)
                                                                                                    	at android.app.ContextImpl.openFileInput(ContextImpl.java:714)
                                                                                                    	at com.android.permissioncontroller.permission.service.BackupHelper.restoreDelayedState(BackupHelper.java:409)
                                                                                                    	at com.android.permissioncontroller.permission.service.PermissionControllerServiceImpl.onRestoreDelayedRuntimePermissionsBackup(PermissionControllerServiceImpl.java:421)
                                                                                                    	at com.android.permissioncontroller.permission.service.PermissionControllerServiceImpl.lambda$onApplyStagedRuntimePermissionBackup$4(PermissionControllerServiceImpl.java:415)
                                                                                                    	at com.android.permissioncontroller.permission.service.PermissionControllerServiceImpl.$r8$lambda$d0Nn-xRapZQvoyXU4vSZgmIHAHQ(Unknown Source:0)
                                                                                                    	at com.android.permissioncontroller.permission.service.PermissionControllerServiceImpl$$ExternalSyntheticLambda10.run(Unknown Source:8)
                                                                                                    	at android.os.AsyncTask$SerialExecutor$1.run(AsyncTask.java:305)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: android.system.ErrnoException: open failed: ENOENT (No such file or directory)
                                                                                                    	at libcore.io.Linux.open(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.open(ForwardingOs.java:563)
                                                                                                    	at libcore.io.BlockGuardOs.open(BlockGuardOs.java:274)
                                                                                                    	at libcore.io.ForwardingOs.open(ForwardingOs.java:563)
                                                                                                    	at android.app.ActivityThread$AndroidOs.open(ActivityThread.java:7810)
                                                                                                    	at libcore.io.IoBridge.open(IoBridge.java:560)
                                                                                                    	at java.io.FileInputStream.<init>(FileInputStream.java:160) 
                                                                                                    	at android.app.ContextImpl.openFileInput(ContextImpl.java:714) 
                                                                                                    	at com.android.permissioncontroller.permission.service.BackupHelper.restoreDelayedState(BackupHelper.java:409) 
                                                                                                    	at com.android.permissioncontroller.permission.service.PermissionControllerServiceImpl.onRestoreDelayedRuntimePermissionsBackup(PermissionControllerServiceImpl.java:421) 
                                                                                                    	at com.android.permissioncontroller.permission.service.PermissionControllerServiceImpl.lambda$onApplyStagedRuntimePermissionBackup$4(PermissionControllerServiceImpl.java:415) 
                                                                                                    	at com.android.permissioncontroller.permission.service.PermissionControllerServiceImpl.$r8$lambda$d0Nn-xRapZQvoyXU4vSZgmIHAHQ(Unknown Source:0) 
                                                                                                    	at com.android.permissioncontroller.permission.service.PermissionControllerServiceImpl$$ExternalSyntheticLambda10.run(Unknown Source:8) 
                                                                                                    	at android.os.AsyncTask$SerialExecutor$1.run(AsyncTask.java:305) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 12:30:32.085  1950-1950  ChimeraRcvrProxy        com.google.android.gms               E  com.google.android.gms.gass.chimera.PackageChangeBroadcastReceiver dropping broadcast android.intent.action.PACKAGE_ADDED
2025-05-24 12:30:32.448   571-862   CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 12:30:32.679  4578-4648  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 12:30:35.413  1447-3771  PhenotypeFlagCommitter  com.google.android.gms.persistent    E  Retrieving snapshot for com.google.android.gms.playlog.uploader failed (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@*********@24.26.32 (230800-*********):53)
                                                                                                    	at bmyl.i(:com.google.android.gms@*********@24.26.32 (230800-*********):13)
                                                                                                    	at bmyl.j(:com.google.android.gms@*********@24.26.32 (230800-*********):42)
                                                                                                    	at bmyl.h(:com.google.android.gms@*********@24.26.32 (230800-*********):4)
                                                                                                    	at com.google.android.gms.clearcut.uploader.QosUploaderChimeraService.g(:com.google.android.gms@*********@24.26.32 (230800-*********):220)
                                                                                                    	at com.google.android.gms.clearcut.uploader.QosUploaderChimeraService.a(:com.google.android.gms@*********@24.26.32 (230800-*********):24)
                                                                                                    	at avkn.call(:com.google.android.gms@*********@24.26.32 (230800-*********):32)
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
                                                                                                    	at adtm.c(:com.google.android.gms@*********@24.26.32 (230800-*********):50)
                                                                                                    	at adtm.run(:com.google.android.gms@*********@24.26.32 (230800-*********):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:30:35.510   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:30:35.513   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:30:35.517   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:30:35.524  4908-4908  AndroidRuntime          com.example.aimusicplayer            E  FATAL EXCEPTION: main (Ask Gemini)
                                                                                                    Process: com.example.aimusicplayer, PID: 4908
                                                                                                    java.lang.NullPointerException: Attempt to invoke virtual method 'int com.example.aimusicplayer.viewmodel.SplashViewModel$NavigationAction.ordinal()' on a null object reference
                                                                                                    	at com.example.aimusicplayer.ui.splash.SplashActivity.lambda$onCreate$2(SplashActivity.java:85)
                                                                                                    	at com.example.aimusicplayer.ui.splash.SplashActivity.$r8$lambda$0SpRHmW7RmTnKlKbCnIHH90wvis(Unknown Source:0)
                                                                                                    	at com.example.aimusicplayer.ui.splash.SplashActivity$$ExternalSyntheticLambda2.onChanged(D8$$SyntheticClass:0)
                                                                                                    	at androidx.lifecycle.LiveData.considerNotify(LiveData.java:133)
                                                                                                    	at androidx.lifecycle.LiveData.dispatchingValue(LiveData.java:151)
                                                                                                    	at androidx.lifecycle.LiveData.setValue(LiveData.java:309)
                                                                                                    	at androidx.lifecycle.MutableLiveData.setValue(MutableLiveData.java:50)
                                                                                                    	at androidx.lifecycle.LiveDataScopeImpl$emit$2.invokeSuspend(CoroutineLiveData.kt:100)
                                                                                                    	at androidx.lifecycle.LiveDataScopeImpl$emit$2.invoke(Unknown Source:8)
                                                                                                    	at androidx.lifecycle.LiveDataScopeImpl$emit$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at androidx.lifecycle.LiveDataScopeImpl.emit(CoroutineLiveData.kt:98)
                                                                                                    	at androidx.lifecycle.FlowLiveDataConversions$asLiveData$1$1.emit(FlowLiveData.kt:82)
                                                                                                    	at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:396)
                                                                                                    	at kotlinx.coroutines.flow.ReadonlyStateFlow.collect(Unknown Source:2)
                                                                                                    	at androidx.lifecycle.FlowLiveDataConversions$asLiveData$1.invokeSuspend(FlowLiveData.kt:81)
                                                                                                    	at androidx.lifecycle.FlowLiveDataConversions$asLiveData$1.invoke(Unknown Source:8)
                                                                                                    	at androidx.lifecycle.FlowLiveDataConversions$asLiveData$1.invoke(Unknown Source:4)
                                                                                                    	at androidx.lifecycle.BlockRunner$maybeRun$1.invokeSuspend(CoroutineLiveData.kt:177)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.internal.DispatchedContinuationKt.resumeCancellableWith(DispatchedContinuation.kt:367)
                                                                                                    	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable(Cancellable.kt:30)
                                                                                                    	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable$default(Cancellable.kt:25)
                                                                                                    	at kotlinx.coroutines.CoroutineStart.invoke(CoroutineStart.kt:110)
                                                                                                    	at kotlinx.coroutines.AbstractCoroutine.start(AbstractCoroutine.kt:126)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch(Builders.common.kt:56)
                                                                                                    	at kotlinx.coroutines.BuildersKt.launch(Unknown Source:1)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch$default(Builders.common.kt:47)
                                                                                                    	at kotlinx.coroutines.BuildersKt.launch$default(Unknown Source:1)
                                                                                                    	at androidx.lifecycle.BlockRunner.maybeRun(CoroutineLiveData.kt:175)
                                                                                                    	at androidx.lifecycle.CoroutineLiveData.onActive(CoroutineLiveData.kt:241)
                                                                                                    	at androidx.lifecycle.LiveData.changeActiveCounter(LiveData.java:405)
                                                                                                    	at androidx.lifecycle.LiveData$ObserverWrapper.activeStateChanged(LiveData.java:481)
                                                                                                    	at androidx.lifecycle.LiveData$LifecycleBoundObserver.onStateChanged(LiveData.java:440)
                                                                                                    	at androidx.lifecycle.LifecycleRegistry$ObserverWithState.dispatchEvent(LifecycleRegistry.kt:322)
                                                                                                    	at androidx.lifecycle.LifecycleRegistry.forwardPass(LifecycleRegistry.kt:258)
                                                                                                    	at androidx.lifecycle.LifecycleRegistry.sync(LifecycleRegistry.kt:294)
                                                                                                    	at androidx.lifecycle.LifecycleRegistry.moveToState(LifecycleRegistry.kt:143)
                                                                                                    	at androidx.lifecycle.LifecycleRegistry.handleLifecycleEvent(LifecycleRegistry.kt:126)
                                                                                                    	at androidx.lifecycle.ReportFragment$Companion.dispatch$lifecycle_runtime_release(ReportFragment.kt:190)
                                                                                                    	at androidx.lifecycle.ReportFragment$LifecycleCallbacks.onActivityPostStarted(ReportFragment.kt:119)
                                                                                                    	at android.app.Activity.dispatchActivityPostStarted(Activity.java:1423)
                                                                                                    	at android.app.Activity.performStart(Activity.java:8404)
2025-05-24 12:30:35.525  4908-4908  AndroidRuntime          com.example.aimusicplayer            E  	at android.app.ActivityThread.handleStartActivity(ActivityThread.java:3670) (Ask Gemini)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.performLifecycleSequence(TransactionExecutor.java:224)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.cycleToPath(TransactionExecutor.java:204)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeLifecycleState(TransactionExecutor.java:176)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:97)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2307)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
                                                                                                    	Suppressed: kotlinx.coroutines.internal.DiagnosticCoroutineContextException: [StandaloneCoroutine{Cancelling}@14f1aec, Dispatchers.Main.immediate]
2025-05-24 12:30:35.889   571-632   InputDispatcher         system_server                        E  But another display has a focused window
                                                                                                      FocusedWindows:
                                                                                                        displayId=6, name='89bd398 com.android.systemui/com.android.systemui.car.distantdisplay.activity.RootTaskViewWallpaperActivity'
                                                                                                        displayId=3, name='164df27 com.android.systemui/com.android.systemui.car.distantdisplay.activity.DistantDisplayActivity'
                                                                                                        displayId=5, name='30138ee com.android.systemui/com.android.systemui.car.distantdisplay.activity.NavigationTaskViewWallpaperActivity'
                                                                                                        displayId=2, name='6e11e28 com.android.car.cluster.osdouble/com.android.car.cluster.osdouble.ClusterOsDoubleActivity'
2025-05-24 12:30:36.158  4578-4648  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 12:30:39.413   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:30:39.413   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:30:39.414   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:30:39.581   571-1190  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 12:30:39.792  4578-4648  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 12:30:41.507  5009-5009  AndroidRuntime          com.example.aimusicplayer            E  FATAL EXCEPTION: main (Ask Gemini)
                                                                                                    Process: com.example.aimusicplayer, PID: 5009
                                                                                                    java.lang.NullPointerException: Attempt to invoke virtual method 'int com.example.aimusicplayer.viewmodel.SplashViewModel$NavigationAction.ordinal()' on a null object reference
                                                                                                    	at com.example.aimusicplayer.ui.splash.SplashActivity.lambda$onCreate$2(SplashActivity.java:85)
                                                                                                    	at com.example.aimusicplayer.ui.splash.SplashActivity.$r8$lambda$0SpRHmW7RmTnKlKbCnIHH90wvis(Unknown Source:0)
                                                                                                    	at com.example.aimusicplayer.ui.splash.SplashActivity$$ExternalSyntheticLambda2.onChanged(D8$$SyntheticClass:0)
                                                                                                    	at androidx.lifecycle.LiveData.considerNotify(LiveData.java:133)
                                                                                                    	at androidx.lifecycle.LiveData.dispatchingValue(LiveData.java:151)
                                                                                                    	at androidx.lifecycle.LiveData.setValue(LiveData.java:309)
                                                                                                    	at androidx.lifecycle.MutableLiveData.setValue(MutableLiveData.java:50)
                                                                                                    	at androidx.lifecycle.LiveDataScopeImpl$emit$2.invokeSuspend(CoroutineLiveData.kt:100)
                                                                                                    	at androidx.lifecycle.LiveDataScopeImpl$emit$2.invoke(Unknown Source:8)
                                                                                                    	at androidx.lifecycle.LiveDataScopeImpl$emit$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at androidx.lifecycle.LiveDataScopeImpl.emit(CoroutineLiveData.kt:98)
                                                                                                    	at androidx.lifecycle.FlowLiveDataConversions$asLiveData$1$1.emit(FlowLiveData.kt:82)
                                                                                                    	at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:396)
                                                                                                    	at kotlinx.coroutines.flow.ReadonlyStateFlow.collect(Unknown Source:2)
                                                                                                    	at androidx.lifecycle.FlowLiveDataConversions$asLiveData$1.invokeSuspend(FlowLiveData.kt:81)
                                                                                                    	at androidx.lifecycle.FlowLiveDataConversions$asLiveData$1.invoke(Unknown Source:8)
                                                                                                    	at androidx.lifecycle.FlowLiveDataConversions$asLiveData$1.invoke(Unknown Source:4)
                                                                                                    	at androidx.lifecycle.BlockRunner$maybeRun$1.invokeSuspend(CoroutineLiveData.kt:177)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.internal.DispatchedContinuationKt.resumeCancellableWith(DispatchedContinuation.kt:367)
                                                                                                    	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable(Cancellable.kt:30)
                                                                                                    	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable$default(Cancellable.kt:25)
                                                                                                    	at kotlinx.coroutines.CoroutineStart.invoke(CoroutineStart.kt:110)
                                                                                                    	at kotlinx.coroutines.AbstractCoroutine.start(AbstractCoroutine.kt:126)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch(Builders.common.kt:56)
                                                                                                    	at kotlinx.coroutines.BuildersKt.launch(Unknown Source:1)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch$default(Builders.common.kt:47)
                                                                                                    	at kotlinx.coroutines.BuildersKt.launch$default(Unknown Source:1)
                                                                                                    	at androidx.lifecycle.BlockRunner.maybeRun(CoroutineLiveData.kt:175)
                                                                                                    	at androidx.lifecycle.CoroutineLiveData.onActive(CoroutineLiveData.kt:241)
                                                                                                    	at androidx.lifecycle.LiveData.changeActiveCounter(LiveData.java:405)
                                                                                                    	at androidx.lifecycle.LiveData$ObserverWrapper.activeStateChanged(LiveData.java:481)
                                                                                                    	at androidx.lifecycle.LiveData$LifecycleBoundObserver.onStateChanged(LiveData.java:440)
                                                                                                    	at androidx.lifecycle.LifecycleRegistry$ObserverWithState.dispatchEvent(LifecycleRegistry.kt:322)
                                                                                                    	at androidx.lifecycle.LifecycleRegistry.forwardPass(LifecycleRegistry.kt:258)
                                                                                                    	at androidx.lifecycle.LifecycleRegistry.sync(LifecycleRegistry.kt:294)
                                                                                                    	at androidx.lifecycle.LifecycleRegistry.moveToState(LifecycleRegistry.kt:143)
                                                                                                    	at androidx.lifecycle.LifecycleRegistry.handleLifecycleEvent(LifecycleRegistry.kt:126)
                                                                                                    	at androidx.lifecycle.ReportFragment$Companion.dispatch$lifecycle_runtime_release(ReportFragment.kt:190)
                                                                                                    	at androidx.lifecycle.ReportFragment$LifecycleCallbacks.onActivityPostStarted(ReportFragment.kt:119)
                                                                                                    	at android.app.Activity.dispatchActivityPostStarted(Activity.java:1423)
                                                                                                    	at android.app.Activity.performStart(Activity.java:8404)
2025-05-24 12:30:41.509  5009-5009  AndroidRuntime          com.example.aimusicplayer            E  	at android.app.ActivityThread.handleStartActivity(ActivityThread.java:3670) (Ask Gemini)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.performLifecycleSequence(TransactionExecutor.java:224)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.cycleToPath(TransactionExecutor.java:204)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeLifecycleState(TransactionExecutor.java:176)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:97)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2307)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
                                                                                                    	Suppressed: kotlinx.coroutines.internal.DiagnosticCoroutineContextException: [StandaloneCoroutine{Cancelling}@14f1aec, Dispatchers.Main.immediate]
2025-05-24 12:30:41.649   571-1274  InputDispatcher         system_server                        E  But another display has a focused window
                                                                                                      FocusedWindows:
                                                                                                        displayId=6, name='89bd398 com.android.systemui/com.android.systemui.car.distantdisplay.activity.RootTaskViewWallpaperActivity'
                                                                                                        displayId=3, name='164df27 com.android.systemui/com.android.systemui.car.distantdisplay.activity.DistantDisplayActivity'
                                                                                                        displayId=5, name='30138ee com.android.systemui/com.android.systemui.car.distantdisplay.activity.NavigationTaskViewWallpaperActivity'
                                                                                                        displayId=2, name='6e11e28 com.android.car.cluster.osdouble/com.android.car.cluster.osdouble.ClusterOsDoubleActivity'
2025-05-24 12:30:41.658  1757-2108  WakeLock                com.google.android.gms               E  IntentOp:.chimera.container.InitConfigOperation ** IS FORCE-RELEASED ON TIMEOUT **
2025-05-24 12:30:41.764  4578-4648  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 12:30:43.619   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:30:43.619   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:30:43.619   571-1917  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 12:30:45.819  1447-3706  bnan                    com.google.android.gms.persistent    E  Phenotype registration failed [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@*********@24.26.32 (230800-*********):53)
                                                                                                    	at bnan.a(:com.google.android.gms@*********@24.26.32 (230800-*********):68)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.d(:com.google.android.gms@*********@24.26.32 (230800-*********):47)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.a(:com.google.android.gms@*********@24.26.32 (230800-*********):126)
                                                                                                    	at avkn.call(:com.google.android.gms@*********@24.26.32 (230800-*********):32)
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
                                                                                                    	at adtm.c(:com.google.android.gms@*********@24.26.32 (230800-*********):50)
                                                                                                    	at adtm.run(:com.google.android.gms@*********@24.26.32 (230800-*********):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 12:30:46.701  1447-4846  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!应用程序点击后直接就闪退了