1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.aimusicplayer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 百度语音SDK所需权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:6:5-66
12-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:7:5-78
13-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:8:5-75
14-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
15-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:9:22-77
16
17    <!-- Android 13 (API 33) 及以上版本需要区分的存储权限 -->
18    <uses-permission
18-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:12:22-78
20        android:maxSdkVersion="32" />
20-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:13:9-35
21    <uses-permission
21-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:14:5-15:38
22        android:name="android.permission.READ_EXTERNAL_STORAGE"
22-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:14:22-77
23        android:maxSdkVersion="32" />
23-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:15:9-35
24
25    <!-- Android 13 新增的精细存储权限 -->
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:18:5-75
26-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:18:22-72
27    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
27-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:19:5-76
27-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:19:22-73
28    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
28-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:20:5-75
28-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:20:22-72
29
30    <!-- 应用所需的其他权限 -->
31    <uses-permission android:name="android.permission.RECORD_AUDIO" />
31-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:23:5-70
31-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:23:22-68
32    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
32-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:24:5-78
32-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:24:22-76
33    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
33-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:25:5-74
33-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:25:22-72
34    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
34-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:26:5-78
34-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:26:22-75
35
36    <!-- 通知权限 (Android 13+) -->
37    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
37-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:29:5-77
37-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:29:22-74
38
39    <!-- 前台服务权限 -->
40    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
40-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:32:5-77
40-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:32:22-74
41
42    <!-- 震动权限 -->
43    <uses-permission android:name="android.permission.VIBRATE" />
43-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:35:5-66
43-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:35:22-63
44    <uses-permission android:name="android.permission.CAMERA" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
44-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:22:5-65
44-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:22:22-62
45    <uses-feature
45-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:25:5-27:36
46        android:name="android.hardware.camera"
46-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:26:9-47
47        android:required="false" />
47-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:27:9-33
48    <uses-feature
48-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:28:5-30:36
49        android:name="android.hardware.camera.front"
49-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:29:9-53
50        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
50-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:30:9-33
51    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
52    <uses-feature
52-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:33:5-35:36
53        android:name="android.hardware.camera.autofocus"
53-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:34:9-57
54        android:required="false" />
54-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:35:9-33
55    <uses-feature
55-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:36:5-38:36
56        android:name="android.hardware.camera.flash"
56-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:37:9-53
57        android:required="false" />
57-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:38:9-33
58    <uses-feature
58-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:39:5-41:36
59        android:name="android.hardware.screen.landscape"
59-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:40:9-57
60        android:required="false" />
60-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:41:9-33
61    <uses-feature
61-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:42:5-44:36
62        android:name="android.hardware.wifi"
62-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:43:9-45
63        android:required="false" />
63-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:44:9-33
64
65    <permission
65-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
66        android:name="com.example.aimusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
66-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
67        android:protectionLevel="signature" />
67-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
68
69    <uses-permission android:name="com.example.aimusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
69-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
69-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
70
71    <application
71-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:37:5-106:19
72        android:name="com.example.aimusicplayer.MusicApplication"
72-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:38:9-41
73        android:allowBackup="true"
73-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:39:9-35
74        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
74-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
75        android:dataExtractionRules="@xml/data_extraction_rules"
75-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:40:9-65
76        android:debuggable="true"
77        android:extractNativeLibs="false"
78        android:fullBackupContent="@xml/backup_rules"
78-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:41:9-54
79        android:icon="@mipmap/ic_launcher"
79-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:42:9-43
80        android:label="轻聆"
80-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:43:9-27
81        android:networkSecurityConfig="@xml/network_security_config"
81-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:48:9-69
82        android:requestLegacyExternalStorage="true"
82-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:46:9-52
83        android:supportsRtl="true"
83-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:44:9-35
84        android:testOnly="true"
85        android:theme="@style/Theme.AIMusicPlayer"
85-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:45:9-51
86        android:usesCleartextTraffic="true" >
86-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:47:9-44
87
88        <!-- 百度语音SDK必要的meta-data配置 -->
89        <meta-data
89-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:52:9-54:41
90            android:name="com.baidu.speech.APP_ID"
90-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:53:13-51
91            android:value="118558442" />
91-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:54:13-38
92        <meta-data
92-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:55:9-57:56
93            android:name="com.baidu.speech.API_KEY"
93-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:56:13-52
94            android:value="l07tTLiM8XdSVcM6Avmv5FG3" />
94-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:57:13-53
95        <meta-data
95-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:58:9-60:64
96            android:name="com.baidu.speech.SECRET_KEY"
96-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:59:13-55
97            android:value="e4DxN5gewACp162txczyVRuJs4UGBhdb" />
97-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:60:13-61
98
99        <!-- 启动页 -->
100        <activity
100-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:63:9-72:20
101            android:name="com.example.aimusicplayer.ui.splash.SplashActivity"
101-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:64:13-53
102            android:exported="true"
102-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:65:13-36
103            android:screenOrientation="landscape"
103-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:66:13-50
104            android:theme="@style/FullScreenTheme" >
104-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:67:13-51
105            <intent-filter>
105-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:68:13-71:29
106                <action android:name="android.intent.action.MAIN" />
106-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:69:17-69
106-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:69:25-66
107
108                <category android:name="android.intent.category.LAUNCHER" />
108-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:70:17-77
108-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:70:27-74
109            </intent-filter>
110        </activity>
111
112        <!-- 登录页 -->
113        <activity
113-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:75:9-79:54
114            android:name="com.example.aimusicplayer.ui.login.LoginActivity"
114-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:76:13-51
115            android:exported="false"
115-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:77:13-37
116            android:screenOrientation="landscape"
116-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:78:13-50
117            android:theme="@style/FullScreenTheme" />
117-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:79:13-51
118
119        <!-- 主界面（已重命名，移除了MainActivity2） -->
120        <activity
120-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:82:9-86:54
121            android:name="com.example.aimusicplayer.ui.main.MainActivity"
121-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:83:13-49
122            android:exported="false"
122-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:84:13-37
123            android:screenOrientation="landscape"
123-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:85:13-50
124            android:theme="@style/FullScreenTheme" />
124-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:86:13-51
125
126        <!-- 播放服务 -->
127        <service
127-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:89:9-92:40
128            android:name="com.example.aimusicplayer.service.UnifiedPlaybackService"
128-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:90:13-59
129            android:enabled="true"
129-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:91:13-35
130            android:exported="false" />
130-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:92:13-37
131
132        <!-- 播放器页面已迁移到Fragment，不再需要单独的Activity -->
133        <!-- PlayerActivity已删除，功能已迁移到PlayerFragment -->
134
135        <provider
136            android:name="androidx.core.content.FileProvider"
136-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:98:13-62
137            android:authorities="com.example.aimusicplayer.provider"
137-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:99:13-60
138            android:exported="false"
138-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:100:13-37
139            android:grantUriPermissions="true" >
139-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:101:13-47
140            <meta-data
140-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:102:13-104:54
141                android:name="android.support.FILE_PROVIDER_PATHS"
141-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:103:17-67
142                android:resource="@xml/file_paths" />
142-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:104:17-51
143        </provider>
144
145        <activity
145-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
146            android:name="com.karumi.dexter.DexterActivity"
146-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
147            android:theme="@style/Dexter.Internal.Theme.Transparent" />
147-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
148        <activity
148-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:47:9-53:63
149            android:name="com.journeyapps.barcodescanner.CaptureActivity"
149-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:48:13-74
150            android:clearTaskOnLaunch="true"
150-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:49:13-45
151            android:screenOrientation="sensorLandscape"
151-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:50:13-56
152            android:stateNotNeeded="true"
152-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:51:13-42
153            android:theme="@style/zxing_CaptureTheme"
153-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:52:13-54
154            android:windowSoftInputMode="stateAlwaysHidden" />
154-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:53:13-60
155
156        <meta-data
156-->[com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:10:9-12:43
157            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
157-->[com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:11:13-84
158            android:value="GlideModule" />
158-->[com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:12:13-40
159
160        <provider
160-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
161            android:name="androidx.startup.InitializationProvider"
161-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
162            android:authorities="com.example.aimusicplayer.androidx-startup"
162-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
163            android:exported="false" >
163-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
164            <meta-data
164-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
165                android:name="androidx.emoji2.text.EmojiCompatInitializer"
165-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
166                android:value="androidx.startup" />
166-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
167            <meta-data
167-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
168                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
168-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
169                android:value="androidx.startup" />
169-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
170            <meta-data
170-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
171                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
171-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
172                android:value="androidx.startup" />
172-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
173        </provider>
174
175        <uses-library
175-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
176            android:name="androidx.window.extensions"
176-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
177            android:required="false" />
177-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
178        <uses-library
178-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
179            android:name="androidx.window.sidecar"
179-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
180            android:required="false" />
180-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
181
182        <service
182-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
183            android:name="androidx.room.MultiInstanceInvalidationService"
183-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
184            android:directBootAware="true"
184-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
185            android:exported="false" />
185-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
186
187        <receiver
187-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
188            android:name="androidx.profileinstaller.ProfileInstallReceiver"
188-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
189            android:directBootAware="false"
189-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
190            android:enabled="true"
190-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
191            android:exported="true"
191-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
192            android:permission="android.permission.DUMP" >
192-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
193            <intent-filter>
193-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
194                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
194-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
194-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
195            </intent-filter>
196            <intent-filter>
196-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
197                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
198            </intent-filter>
199            <intent-filter>
199-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
200                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
201            </intent-filter>
202            <intent-filter>
202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
203                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
204            </intent-filter>
205        </receiver>
206    </application>
207
208</manifest>
