package com.example.aimusicplayer.data.repository;

import com.example.aimusicplayer.data.cache.ApiCacheManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BaseRepository_MembersInjector implements MembersInjector<BaseRepository> {
  private final Provider<ApiCacheManager> apiCacheManagerProvider;

  public BaseRepository_MembersInjector(Provider<ApiCacheManager> apiCacheManagerProvider) {
    this.apiCacheManagerProvider = apiCacheManagerProvider;
  }

  public static MembersInjector<BaseRepository> create(
      Provider<ApiCacheManager> apiCacheManagerProvider) {
    return new BaseRepository_MembersInjector(apiCacheManagerProvider);
  }

  @Override
  public void injectMembers(BaseRepository instance) {
    injectApiCacheManager(instance, apiCacheManagerProvider.get());
  }

  @InjectedFieldSignature("com.example.aimusicplayer.data.repository.BaseRepository.apiCacheManager")
  public static void injectApiCacheManager(BaseRepository instance,
      ApiCacheManager apiCacheManager) {
    instance.apiCacheManager = apiCacheManager;
  }
}
