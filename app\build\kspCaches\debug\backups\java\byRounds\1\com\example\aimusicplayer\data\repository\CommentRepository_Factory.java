package com.example.aimusicplayer.data.repository;

import com.example.aimusicplayer.data.cache.ApiCacheManager;
import com.example.aimusicplayer.data.source.ApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CommentRepository_Factory implements Factory<CommentRepository> {
  private final Provider<ApiService> apiServiceProvider;

  private final Provider<ApiCacheManager> apiCacheManagerProvider;

  public CommentRepository_Factory(Provider<ApiService> apiServiceProvider,
      Provider<ApiCacheManager> apiCacheManagerProvider) {
    this.apiServiceProvider = apiServiceProvider;
    this.apiCacheManagerProvider = apiCacheManagerProvider;
  }

  @Override
  public CommentRepository get() {
    CommentRepository instance = newInstance(apiServiceProvider.get());
    BaseRepository_MembersInjector.injectApiCacheManager(instance, apiCacheManagerProvider.get());
    return instance;
  }

  public static CommentRepository_Factory create(Provider<ApiService> apiServiceProvider,
      Provider<ApiCacheManager> apiCacheManagerProvider) {
    return new CommentRepository_Factory(apiServiceProvider, apiCacheManagerProvider);
  }

  public static CommentRepository newInstance(ApiService apiService) {
    return new CommentRepository(apiService);
  }
}
