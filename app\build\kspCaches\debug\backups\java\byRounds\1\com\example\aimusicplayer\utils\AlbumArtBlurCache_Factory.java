package com.example.aimusicplayer.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AlbumArtBlurCache_Factory implements Factory<AlbumArtBlurCache> {
  private final Provider<Context> contextProvider;

  public AlbumArtBlurCache_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AlbumArtBlurCache get() {
    return newInstance(contextProvider.get());
  }

  public static AlbumArtBlurCache_Factory create(Provider<Context> contextProvider) {
    return new AlbumArtBlurCache_Factory(contextProvider);
  }

  public static AlbumArtBlurCache newInstance(Context context) {
    return new AlbumArtBlurCache(context);
  }
}
