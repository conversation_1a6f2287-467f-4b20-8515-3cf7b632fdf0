package com.example.aimusicplayer.di;

import android.content.Context;
import com.example.aimusicplayer.error.GlobalErrorHandler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ErrorHandlingModule_ProvideGlobalErrorHandlerFactory implements Factory<GlobalErrorHandler> {
  private final Provider<Context> contextProvider;

  public ErrorHandlingModule_ProvideGlobalErrorHandlerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public GlobalErrorHandler get() {
    return provideGlobalErrorHandler(contextProvider.get());
  }

  public static ErrorHandlingModule_ProvideGlobalErrorHandlerFactory create(
      Provider<Context> contextProvider) {
    return new ErrorHandlingModule_ProvideGlobalErrorHandlerFactory(contextProvider);
  }

  public static GlobalErrorHandler provideGlobalErrorHandler(Context context) {
    return Preconditions.checkNotNullFromProvides(ErrorHandlingModule.INSTANCE.provideGlobalErrorHandler(context));
  }
}
