package com.example.aimusicplayer.ui.intelligence;

import android.app.Application;
import com.example.aimusicplayer.data.source.MusicDataSource;
import com.example.aimusicplayer.error.GlobalErrorHandler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class IntelligenceViewModel_Factory implements Factory<IntelligenceViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<MusicDataSource> musicDataSourceProvider;

  private final Provider<GlobalErrorHandler> errorHandlerProvider;

  public IntelligenceViewModel_Factory(Provider<Application> applicationProvider,
      Provider<MusicDataSource> musicDataSourceProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    this.applicationProvider = applicationProvider;
    this.musicDataSourceProvider = musicDataSourceProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public IntelligenceViewModel get() {
    return newInstance(applicationProvider.get(), musicDataSourceProvider.get(), errorHandlerProvider.get());
  }

  public static IntelligenceViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<MusicDataSource> musicDataSourceProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    return new IntelligenceViewModel_Factory(applicationProvider, musicDataSourceProvider, errorHandlerProvider);
  }

  public static IntelligenceViewModel newInstance(Application application,
      MusicDataSource musicDataSource, GlobalErrorHandler errorHandler) {
    return new IntelligenceViewModel(application, musicDataSource, errorHandler);
  }
}
