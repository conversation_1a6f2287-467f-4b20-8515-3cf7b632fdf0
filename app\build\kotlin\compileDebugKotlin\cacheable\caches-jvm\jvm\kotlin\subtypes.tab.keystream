androidx.navigation.NavArgs!androidx.navigation.NavDirections1com.example.aimusicplayer.data.model.BaseResponseandroid.os.Parcelable8com.example.aimusicplayer.data.repository.BaseRepository*com.example.aimusicplayer.service.PlayMode+com.example.aimusicplayer.service.PlayState(androidx.recyclerview.widget.ListAdapter2androidx.recyclerview.widget.DiffUtil.ItemCallback1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolderandroidx.fragment.app.Fragmentandroid.view.View.androidx.recyclerview.widget.DiffUtil.Callbackjava.io.Serializable-com.example.aimusicplayer.utils.NetworkResultkotlin.Enum1com.example.aimusicplayer.viewmodel.FlowViewModel androidx.viewbinding.ViewBinding*com.bumptech.glide.GeneratedAppGlideModule%androidx.multidex.MultiDexApplicationokhttp3.Interceptorandroidx.room.RoomDatabase-androidx.media3.datasource.DataSource.Factory2com.example.aimusicplayer.service.PlayerController+androidx.media3.session.MediaSessionService(androidx.appcompat.app.AppCompatActivityandroid.widget.FrameLayout(com.bumptech.glide.module.AppGlideModule<com.bumptech.glide.load.resource.bitmap.BitmapTransformation#androidx.lifecycle.AndroidViewModelretrofit2.Callback                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       