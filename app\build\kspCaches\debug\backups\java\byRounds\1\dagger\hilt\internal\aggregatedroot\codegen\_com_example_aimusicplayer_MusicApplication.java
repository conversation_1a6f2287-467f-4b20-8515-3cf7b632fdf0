package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.aimusicplayer.MusicApplication",
    rootPackage = "com.example.aimusicplayer",
    originatingRoot = "com.example.aimusicplayer.MusicApplication",
    originatingRootPackage = "com.example.aimusicplayer",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "MusicApplication",
    originatingRootSimpleNames = "MusicApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_example_aimusicplayer_MusicApplication {
}
